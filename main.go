package main

import (
	"bitbucket.org/nuitdevelopers/jira-api/service/auto_update"
	"github.com/go-chi/chi/v5"
	"log"
	"net/http"
	"time"
)

type IntervalResponse struct {
	AutoUpdateInterval int `json:"auto_update_interval"`
}

func main() {
	r := chi.NewRouter()

	err := DbSetup()
	if err != nil {
		log.Fatalf("Error setting up database: %v", err)
	}

	jiraHandler := JiraSetup()
	autoUpdateConfig, err := auto_update.LoadConfig()
	if err != nil {
		log.Fatalf("Error loading auto update config: %v", err)
	}

	autoUpdateTime := time.Duration(autoUpdateConfig.AutoUpdateInterval) * time.Second

	if autoUpdateConfig.AutoUpdateEnabled {
		scheduler(autoUpdateTime, jiraHandler)
	}

	NewJiraRouter(r, autoUpdateConfig, jiraHandler)

	err = http.ListenAndServe(":50620", r)
	if err != nil { // Check for errors when starting the server
		log.Fatal(err) // Log the error and exit if the server fails to start
	}
}

package model

import "time"

type Issue struct {
	Key                  string     `json:"key"`
	Summary              string     `json:"summary"`
	AssigneeName         *string    `json:"assigneeName"`
	SecondAssigneeName   *string    `json:"secondAssigneeDisplayName"`
	ReporterName         *string    `json:"reporterDisplayName"`
	StatusName           string     `json:"statusName"`
	StatusId             int        `json:"statusId"`
	IssueTypeName        string     `json:"issueTypeName"`
	PriorityId           int        `json:"priorityId"`
	PriorityName         string     `json:"priorityName"`
	Created              time.Time  `json:"created"`
	Updated              time.Time  `json:"updated"`
	Labels               *string    `json:"labels"`         // optional
	Resolution           *string    `json:"resolution"`     // optional
	ResolutionDate       *time.Time `json:"resolutionDate"` // optional
	TimeOriginalEstimate *int       `json:"timeOriginalEstimate"`
	TimeRemaining        *int       `json:"timeRemaining"`
	Components           *string    `json:"components"`
	ParentKey            *string    `json:"parentKey"`
	ParentName           *string    `json:"parentName"`
	SprintName           *string    `json:"sprintName"`
	Client               *string    `json:"client"` // optional
	Rank                 *string    `json:"rank"`
	StoryPoints          *int       `json:"storyPoints"`
	ProjectKey           string     `json:"projectKey"`
	ProjectName          string     `json:"projectName"`
	DueDate              *time.Time `json:"dueDate"` // optional
}

type Issues []Issue

package main

import (
	issuesMysql "bitbucket.org/nuitdevelopers/jira-api/service/issues/repository/mysql"
	statusChangesMysql "bitbucket.org/nuitdevelopers/jira-api/service/statuschanges/repository/mysql"
	"fmt"
	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"os"
)

func DbSetup() error {
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	dbPassword := os.Getenv("DB_PASSWORD")
	if dbPassword == "" {
		log.Fatal("DB_PASSWORD is not set in environment")
	}

	user := os.Getenv("DB_USER")
	if user == "" {
		log.Fatal("DB_USER is not set in environment")
	}
	host := os.Getenv("DB_HOST")
	if host == "" {
		log.Fatal("DB_HOST is not set in environment")
	}
	port := os.Getenv("DB_PORT")
	if port == "" {
		log.Fatal("DB_PORT is not set in environment")
	}
	dbName := os.Getenv("DB_NAME")
	if dbName == "" {
		log.Fatal("DB_NAME is not set in environment")
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, dbPassword, host, port, dbName)

	gormDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	issuesMysql.Db = gormDB
	statusChangesMysql.Db = gormDB
	return err
}

# Jira API Service

## Overview

This is a Go-based service for interacting with Jira APIs, including issue management and status change tracking. It implements a modular, clean architecture for robust integration with the Jira API, supporting high-throughput batch loading of issues and status changes into a relational database. The codebase is structured for extensibility, testability, and operational safety, with clear separation of concerns between repository, usecase, and delivery layers.

## Core Features
- **Batch import of Jira issues** with efficient pagination and configurable batch size for both API and DB operations.
- **Import and persist status change history** for each issue, enabling audit and analytics use cases.
- **Centralized configuration** for API/DB batch sizes, rate limits, and credentials via `internal/config/config.go`.
- **Safe, high-performance batch writes** to the database, avoiding MySQL placeholder limits and reducing transaction overhead.
- **Centralized parsing and transformation** of Jira data in the parser layer for consistency and maintainability.
- **Automatic deletion of stale data** before each import to guarantee data freshness and prevent duplication.
- **Rate limiting and retry logic** for API requests to handle Jira quotas and transient errors.

## Data Model
- **Issues**: Richly-typed Jira tasks (see `model/issues.go`) with all core fields (key, status, assignee, timestamps, etc.).
- **StatusChanges**: Full status transition history for each issue (see `model/statuschanges.go`).

## Architecture
- **Repository Layer**: Handles all external I/O (Jira API, DB), including pagination, batching, and error handling. Implements conversion between DB and domain models.
- **Usecase Layer**: Encapsulates business logic for parsing, transforming, and batch-saving data, as well as orchestrating import flows and data cleanup.
- **Delivery Layer**: Entry points (handlers) for running import flows, suitable for HTTP endpoints, scheduled jobs, or CLI triggers.

## Quick Start
1. Configure connection parameters, batch sizes, and rate limits in `service/jira/internal/config/config.go`.
2. Run the service — issues and status changes will be imported and persisted automatically via the usecase layer.

## Example Code
```go
jiraRepo := JiraRepository.NewJiraRepository()
jiraUC := jiraUseCase.NewJiraUseCase(jiraRepo, issueUC, statusChangesUC)
jiraHandler := jiraFunction.NewJiraHandler(jiraUC)
err := jiraHandler.ProcessAndSaveAll("")
if err != nil {
    log.Printf("Error processing Jira data: %v", err)
}
```

## Error Handling
- All DB writes are batched to avoid exceeding MySQL placeholder limits and to optimize throughput.
- API errors and rate limits are handled with automatic retries and exponential backoff.
- Parsing errors are logged and skipped, ensuring that faulty records do not block the import process.



## HTTP Routes: Example Requests & Responses

### Get update status
**Request:**
```
GET /status/updating
```
**Response:**
```
{
  "updating": true
}
```

### Manually update issues
**Request:**
```
POST /issues/update
```
**Response:**
```
{
  "message": "Issues updated successfully"
}
```

### Get auto-update interval
**Request:**
```
GET /auto-update/interval
```
**Response:**
```
{
  "auto_update_interval": 60 seconds
}
```

### Set auto-update interval
**Request:**
```
POST /auto-update/interval
Content-Type: application/json
{
  "auto_update_interval": 120
}
```
**Response:**
```
{
  "message": "Interval updated"
}
```

### Stop auto-update
**Request:**
```
POST /auto-update/stop
```
**Response:**
```
{
  "message": "Auto update stopped"
}
```

### Start auto-update
**Request:**
```
POST /auto-update/start
```
**Response:**
```
{
  "message": "Auto update started"
}
```

### Get auto-update status
**Request:**
```
GET /auto-update/status
```
**Response:**
```
{
  "auto_update": "enabled"
}
```

## Router & Scheduling Mechanism
- All routes are registered in `router.go` .
- Auto-update logic is implemented in the `scheduler` package.
- Canceling and restarting auto-update uses Go channels and context cancellation:
- When interval or status changes, the previous goroutine is stopped via `cancel()` and a channel is used to wait for its completion before starting a new one.

## JSON Config
- Main config file: `service/jira/internal/config/config.go` and `config/config.json`.
- Contains DB credentials, batch sizes, rate limits, and auto-update settings.
- Usage: loaded at startup and used to control import behavior and scheduling.

## Example Usage
```go
// Start auto-update with interval from config
scheduler.StartAutoUpdate(config.AutoUpdateInterval, jiraHandler)

// Change interval via HTTP
// POST /auto-update/interval {"auto_update_interval": 60}
```

## Example JSON Config

`config/config.json` example:
```json
{
  "batch_size": 100,
  "rate_limit": 50,
  "auto_update_enabled": true,
  "auto_update_interval": 60
}
```
- `auto_update_enabled`: enables/disables auto-update scheduler
- `auto_update_interval`: interval in seconds for auto-update

### Usage in Code
- Config is loaded at startup in `main.go` and passed to router and scheduler:
```go
import "service/jira/internal/config/config"
...
autoUpdateConfig, err := config.LoadConfig()
if autoUpdateConfig.AutoUpdateEnabled {
    scheduler.StartAutoUpdate(time.Duration(autoUpdateConfig.AutoUpdateInterval)*time.Second, jiraHandler)
}
```
- You can change interval or enable/disable auto-update via HTTP endpoints, which update the scheduler using values from config.



## Database Setup

The service automatically creates the necessary database tables using the names specified in your `config.json` file. Make sure your database connection is properly configured and the specified table names follow your database naming conventions.

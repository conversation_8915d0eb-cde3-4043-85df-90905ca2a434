package auto_update

import (
	"bitbucket.org/nuitdevelopers/jira-api/config"
	"encoding/json"
	"fmt"
	"os"
)

type AutoUpdateConfig struct {
	AutoUpdateEnabled  bool `json:"auto_update_enabled"`
	AutoUpdateInterval int  `json:"auto_update_interval"`
	IsUpdating         bool
}

func LoadConfig() (*AutoUpdateConfig, error) {
	file, err := os.Open("config/config.json")
	if err != nil {
		return nil, fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	var cfg config.JsonConfig
	err = json.NewDecoder(file).Decode(&cfg)
	if err != nil {
		return nil, fmt.Errorf("error decoding JSON: %v", err)
	}

	autoUpdateConfig := &AutoUpdateConfig{
		AutoUpdateEnabled:  cfg.AutoUpdateEnabled,
		AutoUpdateInterval: cfg.AutoUpdateInterval,
		IsUpdating:         false,
	}

	if autoUpdateConfig.AutoUpdateInterval <= 0 {
		return nil, fmt.Errorf("invalid auto-update interval: %d, must be greater than 0", autoUpdateConfig.AutoUpdateInterval)
	}

	return autoUpdateConfig, nil
}

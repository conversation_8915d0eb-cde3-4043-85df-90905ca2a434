package mysql

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"gorm.io/gorm"
	"log"
)

var (
	Db *gorm.DB
)

type StatusChangeRepository struct {
}

func NewStatusChangeRepository() *StatusChangeRepository {
	return &StatusChangeRepository{}
}

func (h StatusChangeRepository) Create(command model.StatusChange) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		sm := StatusChangeFromModel(command)
		err := tx.Create(&sm).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h StatusChangeRepository) CreateMany(commands model.StatusChanges) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		var sms []StatusChange
		for _, c := range commands {
			sms = append(sms, StatusChangeFromModel(c))
		}
		err := tx.Create(&sms).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h StatusChangeRepository) Update(commands []model.StatusChanges) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := h.Delete()
		if err != nil {
			return err
		}
		for _, command := range commands {
			err := h.CreateMany(command)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h StatusChangeRepository) Delete() error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := tx.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&StatusChange{}).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

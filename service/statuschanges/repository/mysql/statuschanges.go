package mysql

import (
	"bitbucket.org/nuitdevelopers/jira-api/config"
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"log"
	"time"
)

type StatusChange struct {
	Key            string  `gorm:"column:key;size:255;primaryKey"`
	ToStatusName   string  `gorm:"column:toStatusName;size:255"`
	ToStatusId     int     `gorm:"column:toStatusId"`
	FromStatusName *string `gorm:"column:fromStatusName;size:255"`
	FromStatusId   *int    `gorm:"column:fromStatusId"`
	Date           uint64  `gorm:"column:date"`
}

func (o StatusChange) TableName() string {
	config, err := config.LoadConfig()
	if err != nil {
		log.Printf("Error loading config: %v", err)
	}
	return config.StatusTable
}
func (o StatusChange) Model() model.StatusChange {
	return model.StatusChange{
		Key:            o.Key,
		ToStatusName:   o.ToStatusName,
		ToStatusId:     o.ToStatusId,
		FromStatusName: o.FromStatusName,
		FromStatusId:   o.FromStatusId,
		Date:           time.Unix(0, int64(o.Date)),
	}
}

func StatusChangeFromModel(mo model.StatusChange) StatusChange {
	o := StatusChange{
		Key:            mo.Key,
		ToStatusName:   mo.ToStatusName,
		ToStatusId:     mo.ToStatusId,
		FromStatusName: mo.FromStatusName,
		FromStatusId:   mo.FromStatusId,
		Date:           uint64(mo.Date.UnixNano()),
	}
	return o
}

type StatusChanges []StatusChange

func (os StatusChanges) ModelStatusChange() model.StatusChanges {
	var msc model.StatusChanges
	for _, o := range os {
		msc = append(msc, o.Model())
	}
	return msc
}

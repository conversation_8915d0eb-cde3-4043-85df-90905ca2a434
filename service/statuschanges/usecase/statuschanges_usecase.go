package usecase

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/statuschanges"
	"log"
)

type StatusChangeUseCase struct {
	repo statuschanges.Repository
}

func NewStatusChangeUseCase(repo statuschanges.Repository) *StatusChangeUseCase {
	return &StatusChangeUseCase{
		repo: repo,
	}
}

func (u *StatusChangeUseCase) Create(input model.StatusChange) error {
	err := u.repo.Create(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (u *StatusChangeUseCase) CreateMany(input model.StatusChanges) error {
	err := u.repo.CreateMany(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (u *StatusChangeUseCase) Update(input []model.StatusChanges) error {
	err := u.repo.Update(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (u *StatusChangeUseCase) Delete() error {
	err := u.repo.Delete()
	return err
}

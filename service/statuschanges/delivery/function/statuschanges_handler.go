package function

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/statuschanges"
	"log"
)

type StatusChangesHandler struct {
	uc statuschanges.UseCase
}

func NewStatusChangesHandler(useCase statuschanges.UseCase) *StatusChangesHandler {
	return &StatusChangesHandler{
		uc: useCase,
	}
}

func (h *StatusChangesHandler) Create(input model.StatusChange) error {
	err := h.uc.Create(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (h *StatusChangesHandler) CreateMany(input model.StatusChanges) error {
	err := h.uc.CreateMany(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (h *StatusChangesHandler) Update(input []model.StatusChanges) error {
	err := h.uc.Update(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (h *StatusChangesHandler) Delete() error {
	err := h.uc.Delete()
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

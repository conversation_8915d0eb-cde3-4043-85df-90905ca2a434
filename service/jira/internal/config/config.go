package config

import (
	"bitbucket.org/nuitdevelopers/jira-api/service/jira/internal/rateLimiter"
	"encoding/json"
	"github.com/joho/godotenv"
	"log"
	"net/http"
	"os"
)

type Config struct {
	Client      http.Client
	RateLimiter *rateLimiter.RateLimiter
	baseURL     string
	CloudId     string
	Token       string
	RateLimit   int
	MaxResults  int // For API pagination
	DBBatchSize int // For database operations
}

type jsonConfig struct {
	RateLimit   int `json:"requests_per_second"`
	MaxResults  int `json:"max_results"`
	DBBatchSize int `json:"db_batch_size"`
}

func LoadConfig() *Config {

	err, token, cloudId := loadEnvValues()
	if err != nil {
		log.Fatal("Fehler beim Laden der Umgebungsvariablen: ", err)
	}

	file, err := os.Open("config/config.json")
	if err != nil {
		return &Config{RateLimit: 1}
	}
	defer file.Close()

	var cfg jsonConfig
	err2 := json.NewDecoder(file).Decode(&cfg)
	if err2 != nil {
		log.Fatal("Fehler beim Dekodieren der JSON-Konfiguration: ", err2)
	}

	return &Config{
		RateLimiter: rateLimiter.NewRateLimiter(cfg.RateLimit),
		MaxResults:  cfg.MaxResults,
		DBBatchSize: cfg.DBBatchSize,
		Token:       token,
		CloudId:     cloudId,
	}
}

func loadEnvValues() (error, string, string) {
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Fehler beim Laden der .env-Datei")
	}

	token := os.Getenv("JIRA_TOKEN")
	if token == "" {
		log.Fatal("Jira token nicht gesetzt")
	}

	cloudId := os.Getenv("JIRA_CLOUD_ID")
	if cloudId == "" {
		log.Fatal("Jira cloud ID nicht gesetzt")
	}

	return err, token, cloudId
}

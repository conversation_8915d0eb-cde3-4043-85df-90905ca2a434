package processor

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/jira/internal/parser"
	"fmt"
	"strconv"
	"strings"
)

type IssueProcessor struct{}

func (p *IssueProcessor) ProcessIssue(issue interface{}) (model.Issue, error) {
	issueMap := issue.(map[string]interface{})
	fields := issueMap["fields"].(map[string]interface{})

	statusId, err := strconv.Atoi(parser.GetNestedString(fields, "status", "id"))
	if err != nil {
		return model.Issue{}, fmt.Errorf("error parsing status id: %v", err)
	}
	priorityId, err := strconv.Atoi(parser.GetNestedString(fields, "priority", "id"))
	if err != nil {
		return model.Issue{}, fmt.Errorf("error parsing priority id: %v", err)
	}

	labelsStr := strings.Join(parser.GetStringSlice(fields, "labels"), ",")
	componentsStr := strings.Join(parser.GetComponentNames(fields), ",")

	jiraIssue := model.Issue{
		Key:                  issueMap["key"].(string),
		Summary:              parser.GetString(fields, "summary"),
		AssigneeName:         parser.GetStringPtr(parser.GetNestedString(fields, "assignee", "displayName")),
		SecondAssigneeName:   parser.GetStringPtr(parser.GetCustomString(fields, "customfield_10027", "displayName")),
		StatusName:           parser.GetNestedString(fields, "status", "name"),
		StatusId:             statusId,
		IssueTypeName:        parser.GetNestedString(fields, "issuetype", "name"),
		PriorityId:           priorityId,
		PriorityName:         parser.GetNestedString(fields, "priority", "name"),
		ReporterName:         parser.GetStringPtr(parser.GetNestedString(fields, "reporter", "displayName")),
		Created:              parser.GetTime(fields, "created"),
		Updated:              parser.GetTime(fields, "updated"),
		Labels:               parser.GetStringPtr(labelsStr),
		Resolution:           parser.GetStringPtr(parser.GetResolution(fields)),
		ResolutionDate:       parser.GetTimePtr(parser.GetTime(fields, "resolutiondate")),
		TimeOriginalEstimate: parser.GetIntPtr(fields, "timeoriginalestimate"),
		TimeRemaining:        parser.GetIntPtr(fields, "timeremaining"),
		Components:           parser.GetStringPtr(componentsStr),
		ParentKey:            parser.GetStringPtr(parser.GetParentField(fields, "key")),
		ParentName:           parser.GetStringPtr(parser.GetParentField(fields, "summary")),
		SprintName:           parser.GetStringPtr(parser.GetFieldFromChangelog(issueMap["changelog"].(map[string]interface{}), "Sprint")),
		Client:               parser.GetStringPtr(parser.GetString(fields, "client")),
		Rank:                 parser.GetStringPtr(parser.GetFieldFromChangelog(issueMap["changelog"].(map[string]interface{}), "Rank")),
		StoryPoints:          parser.GetIntPtr(fields, "storypoints"),
		ProjectKey:           parser.GetNestedString(fields, "project", "key"),
		ProjectName:          parser.GetNestedString(fields, "project", "name"),
		DueDate:              parser.GetTimePtr(parser.GetTime(fields, "duedate")),
	}

	if storyPointsCustom := parser.GetIntPtr(fields, "customfield_10016"); storyPointsCustom != nil {
		jiraIssue.StoryPoints = storyPointsCustom
	}

	return jiraIssue, nil
}

package parser

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"strconv"
	"time"
)

func GetString(m map[string]interface{}, key string) string {
	v, ok := m[key]
	if ok && v != nil {
		s, ok := v.(string)
		if ok {
			return s
		}
	}
	return ""
}

func GetStringSlice(m map[string]interface{}, key string) []string {
	v, ok := m[key]
	if ok && v != nil {
		arr, ok := v.([]interface{})
		if ok {
			res := []string{}
			for _, a := range arr {
				s, ok := a.(string)
				if ok {
					res = append(res, s)
				}
			}
			return res
		}
	}
	return nil
}
func GetNestedString(m map[string]interface{}, parentKey, childKey string) string {
	parent, exists := m[parentKey]
	if !exists || parent == nil {
		return ""
	}

	parentMap, isMap := parent.(map[string]interface{})
	if !isMap {
		return ""
	}

	childValue, isString := parentMap[childKey].(string)
	if !isString {
		return ""
	}
	return childValue
}

func GetComponentNames(fields map[string]interface{}) []string {
	components, exists := fields["components"]
	if !exists || components == nil {
		return nil
	}

	componentsArr, isArr := components.([]interface{})
	if !isArr {
		return nil
	}

	result := make([]string, 0, len(componentsArr))
	for _, comp := range componentsArr {
		compMap, isMap := comp.(map[string]interface{})
		if !isMap {
			continue
		}
		name, isString := compMap["name"].(string)
		if isString {
			result = append(result, name)
		}
	}
	return result
}

func GetCustomString(m map[string]interface{}, key, subkey string) string {
	v, ok := m[key]
	if !ok || v == nil {
		return ""
	}

	if subkey == "" {
		s, ok := v.(string)
		if ok {
			return s
		}
	}

	sub, ok := v.(map[string]interface{})
	if !ok {
		return ""
	}

	s, ok := sub[subkey].(string)
	if !ok {
		return ""
	}
	return s
}

func GetResolution(fields map[string]interface{}) string {
	v, ok := fields["resolution"]
	if !ok || v == nil {
		return ""
	}

	s, ok := v.(string)
	if ok {
		return s
	}

	m, ok := v.(map[string]interface{})
	if ok {
		name, ok := m["name"].(string)
		if ok {
			return name
		}
	}
	return ""
}

func GetParentField(fields map[string]interface{}, field string) string {
	parent, ok := fields["parent"]
	if !ok || parent == nil {
		return ""
	}
	parentMap, ok := parent.(map[string]interface{})
	if !ok {
		return ""
	}

	switch field {
	case "key":
		key, ok := parentMap["key"].(string)
		if ok {
			return key
		}
	case "summary":
		fieldsMap, ok := parentMap["fields"].(map[string]interface{})
		if !ok {
			return ""
		}
		summary, ok := fieldsMap["summary"].(string)
		if ok {
			return summary
		}
	}

	return ""
}

func GetFieldFromChangelog(changelog map[string]interface{}, field string) string {
	if changelog == nil {
		return ""
	}
	histories, ok := changelog["histories"].([]interface{})
	if !ok {
		return ""
	}
	for _, history := range histories {
		historyMap, ok := history.(map[string]interface{})
		if !ok {
			continue
		}
		items, ok := historyMap["items"].([]interface{})
		if !ok {
			continue
		}
		for _, item := range items {
			itemMap, ok := item.(map[string]interface{})
			if !ok {
				continue
			}
			if itemMap["field"] == field && itemMap["fieldtype"] == "custom" {
				if toString, ok := itemMap["toString"].(string); ok && toString != "" {
					return toString
				}
			}
		}
	}
	return ""
}

func GetTime(m map[string]interface{}, key string) time.Time {
	v, ok := m[key]
	if !ok || v == nil {
		return time.Time{}
	}

	s, ok := v.(string)
	if !ok {
		return time.Time{}
	}

	t, err := time.Parse("2006-01-02T15:04:05.000-0700", s)
	if err != nil {
		return time.Time{}
	}
	return t
}
func GetStringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

func GetTimePtr(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}

func GetIntPtr(m map[string]interface{}, key string) *int {
	v, ok := m[key]
	if !ok || v == nil {
		return nil
	}

	switch val := v.(type) {
	case float64:
		intVal := int(val)
		return &intVal
	case int:
		return &val
	}
	return nil
}

func ProcessStatusChangesFromIssue(issue interface{}) ([]model.StatusChange, error) {
	issueMap := issue.(map[string]interface{})
	key := issueMap["key"].(string)

	var statusChanges []model.StatusChange

	// Get changelog
	changelog, exists := issueMap["changelog"].(map[string]interface{})
	if !exists || changelog == nil {
		return statusChanges, nil
	}

	histories, exists := changelog["histories"].([]interface{})
	if !exists {
		return statusChanges, nil
	}

	// Add initial status change (creation)
	fields := issueMap["fields"].(map[string]interface{})
	currentStatusName := GetNestedString(fields, "status", "name")
	currentStatusId, _ := strconv.Atoi(GetNestedString(fields, "status", "id"))
	createdTime := GetTime(fields, "created")

	// Track if we found any status changes in history
	foundStatusChanges := false

	// Process history to find status changes
	for _, history := range histories {
		historyMap := history.(map[string]interface{})

		items, exists := historyMap["items"].([]interface{})
		if !exists {
			continue
		}

		for _, item := range items {
			itemMap := item.(map[string]interface{})

			// Check if this is a status change
			if field, exists := itemMap["field"].(string); exists && field == "status" {
				foundStatusChanges = true

				// Parse the history created date
				createdStr, exists := historyMap["created"].(string)
				if !exists {
					continue
				}

				changeDate, err := time.Parse("2006-01-02T15:04:05.000-0700", createdStr)
				if err != nil {
					continue
				}

				// Get from and to status information
				fromString, _ := itemMap["fromString"].(string)
				toString, _ := itemMap["toString"].(string)
				from, _ := itemMap["from"].(string)
				to, _ := itemMap["to"].(string)

				var fromStatusId *int
				var toStatusId int

				if from != "" {
					if id, err := strconv.Atoi(from); err == nil {
						fromStatusId = &id
					}
				}

				if to != "" {
					if id, err := strconv.Atoi(to); err == nil {
						toStatusId = id
					}
				}

				var fromStatusName *string
				if fromString != "" {
					fromStatusName = &fromString
				}

				statusChange := model.StatusChange{
					Key:            key,
					ToStatusName:   toString,
					ToStatusId:     toStatusId,
					FromStatusName: fromStatusName,
					FromStatusId:   fromStatusId,
					Date:           changeDate,
				}

				statusChanges = append(statusChanges, statusChange)
			}
		}
	}

	// If no status changes found in history, add creation as initial status change
	if !foundStatusChanges {
		initialChange := model.StatusChange{
			Key:            key,
			ToStatusName:   currentStatusName,
			ToStatusId:     currentStatusId,
			FromStatusName: nil,
			FromStatusId:   nil,
			Date:           createdTime,
		}
		statusChanges = append(statusChanges, initialChange)
	}

	return statusChanges, nil
}

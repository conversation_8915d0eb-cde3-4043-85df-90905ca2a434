package rateLimiter

import "time"

type RateLimiter struct {
	ticker   *time.Ticker
	requests chan struct{}
	done     chan bool
}

// Create new rate limiter with specified requests per second
func NewRateLimiter(requestsPerSecond int) *RateLimiter {
	rl := &RateLimiter{
		ticker:   time.NewTicker(time.Second / time.Duration(requestsPerSecond)),
		requests: make(chan struct{}, requestsPerSecond),
		done:     make(chan bool),
	}

	go rl.run()
	return rl
}

func (rl *RateLimiter) run() {
	for {
		select {
		case <-rl.ticker.C:
			select {
			case rl.requests <- struct{}{}:
			default:
			}
		case <-rl.done:
			rl.ticker.Stop()
			return
		}
	}
}

func (rl *RateLimiter) Wait() {
	<-rl.requests
}

func (rl *RateLimiter) Stop() {
	close(rl.done)
}

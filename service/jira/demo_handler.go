package jira

import (
	"bitbucket.org/nuitdevelopers/jira-api/service/issues"
	issueFunction "bitbucket.org/nuitdevelopers/jira-api/service/issues/delivery/function"
	"bitbucket.org/nuitdevelopers/jira-api/service/statuschanges"
	statusChangeFunction "bitbucket.org/nuitdevelopers/jira-api/service/statuschanges/delivery/function"
)

func Demo(issuesUC issues.UseCase, statuschangesUC statuschanges.UseCase) {

	/*statusChangeTime, _ := time.Parse("2006-01-02T15:04:05.000-0700", "2025-07-18T16:32:51.167+0200")
	createdTime, _ := time.Parse("2006-01-02T15:04:05.000-0700", "2025-06-17T15:20:28.383+0200")
	updatedTime, _ := time.Parse("2006-01-02T15:04:05.000-0700", "2025-07-08T10:50:12.994+0200")
	resolutionDate, _ := time.Parse("2006-01-02T15:04:05.000-0700", "2025-07-09T09:00:00.000+0200")
	dueDate, _ := time.Parse("2006-01-02", "2025-07-20")

	issueData := model.Issue{
		Key:                  "NUIT-2480",
		Summary:              "Delivery Documents - Check order of shop & delivery events and find correct way to send calls",
		AssigneeName:         "Gernot Dannereder",
		SecondAssigneeName:   "Sebastian Gorzkowski",
		ReporterName:         "Felix Mitterauer",
		StatusName:           "To Do",
		StatusId:             10026,
		IssueTypeName:        "Task",
		PriorityId:           2,
		PriorityName:         "High",
		Created:              createdTime,
		Updated:              updatedTime,
		Labels:               "cidan, infoscreen",
		Resolution:           "Fixed",
		ResolutionDate:       resolutionDate,
		TimeOriginalEstimate: 28800,
		TimeRemaining:        0,
		Components:           "Frontend",
		ParentKey:            "NUIT-2385",
		ParentName:           "Orders IMS Next",
		SprintName:           "nuEvo Sprint 2025/KW28-29",
		Client:               "Cidan",
		Rank:                 "Ranked higher",
		StoryPoints:          5,
		ProjectKey:           "NUIT",
		ProjectName:          "nuEvolution",
		DueDate:              dueDate,
	}

	var fromStatusName *string = nil
	var fromStatusId *int = nil

	statusChangeData := model.StatusChange{
		Key:            "NUIT-2480",
		ToStatusName:   "To Do",
		ToStatusId:     10026,
		FromStatusName: fromStatusName,
		FromStatusId:   fromStatusId,
		Date:           statusChangeTime,
	}

	//ab hier kopieren
	handler1 := issueFunction.NewIssueHandler(issuesUC)
	err := handler1.Create(issueData)
	if err != nil {
		return
	}

	handler2 := statusChangeFunction.NewStatusChangesHandler(statuschangesUC)
	err = handler2.Create(statusChangeData)
	if err != nil {
		return
	}*/

	handler1 := issueFunction.NewIssueHandler(issuesUC)
	err := handler1.Delete()
	if err != nil {
		return
	}

	handler2 := statusChangeFunction.NewStatusChangesHandler(statuschangesUC)
	err = handler2.Delete()
	if err != nil {
		return
	}

	/*
		err = handler1.CreateMany()
		if err != nil {
			return
		}

		err = handler2.CreateMany()
		if err != nil {
			return
		}*/
}

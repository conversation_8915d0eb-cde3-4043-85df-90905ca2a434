# Jira API Module

## Overview
This module provides a clean architecture for integrating Jira API with a Go backend, supporting scalable batch processing, rate limiting, and robust parsing for issues and status changes. It is designed for extensibility and safe database operations.

## Structure
- **internal/config/**: Configuration for Jira client (API batch size, DB batch size, rate limit, credentials).
- **internal/parser/**: Universal parsing helpers for Jira API responses (used everywhere).
- **internal/rateLimiter/**: Rate limiting for safe API requests.
- **repository/**: API layer for fetching issues and status changes from Jira (pagination, batching).
- **usecase/**: Business logic for processing, transforming, and saving issues/status changes in batches.
- **delivery/function/**: Handlers for orchestrating usecase logic, suitable for HTTP or CLI entrypoints.

## Key Concepts
- **Batch Processing**: All DB writes use batches (default: 500 records) to avoid MySQL placeholder limits and improve performance. Batch size is configurable via `config.go`.
- **API Pagination**: Jira API requests use `maxResults` (default: 100) for safe pagination. All pages are fetched sequentially.
- **Rate Limiting**: API requests are throttled to avoid hitting Jira rate limits (configurable).
- **Parser Layer**: All field extraction and type conversion logic is centralized in `internal/parser/parser.go` for consistency and maintainability.
- **Extensibility**: The architecture allows easy addition of new business logic, endpoints, or integrations.

## Main Flows
### Issue Import
- `FetchAllIssues(ctx, jql)` (repository): Fetches all issues from Jira with pagination and rate limiting.
- `ProcessAndSaveIssues(ctx, jql)` (usecase): Parses issues, transforms them, and saves to DB in batches.

### Status Change Import
- `FetchIssuesAndStatuses(ctx, jql)` (repository): Fetches all issues and extracts status change history.
- `ProcessAndSaveStatusChanges(ctx, jql)` (usecase): Saves status changes to DB in batches.

### Handler Layer
- `JiraHandler` (delivery/function): Entry point for orchestrating full import flows, suitable for HTTP endpoints or CLI jobs.

## Configuration
- All batch sizes, rate limits, and credentials are set in `internal/config/config.go` and loaded by the client.
- Example config fields:
  - `BatchSize` (API requests)
  - `DBBatchSize` (DB operations)
  - `MaxResults` (API pagination)
  - `RateLimit` (API requests per second)

## Error Handling
- All DB operations are batched to avoid `Error 1390 (HY000): Prepared statement contains too many placeholders`.
- API errors and rate limits are handled with retries and backoff.
- All parsing errors are logged and skipped, ensuring robust imports.

## Deleting Issues
Before importing new data from Jira, all old issues are automatically deleted using the `Delete` method in the usecase layer. This ensures that the database always contains up-to-date information. You can also manually trigger deletion via the usecase:

- `issuesUseCase.Delete()`: deletes all issues from the database.
- `statusChangesUseCase.Delete()`: deletes all status changes.

Deletion occurs before loading new data to prevent duplication and outdated records.

## Extending the Module
- Add new parsing logic in `internal/parser/parser.go`.
- Add new business flows in `usecase/`.
- Add new handlers in `delivery/function/`.
- Update config in `internal/config/config.go` for new limits or credentials.

## Example Usage (Code)
```go
// Repository setup
jiraRepo := JiraRepository.NewJiraRepository()
// Usecase setup
jiraUC := jiraUseCase.NewJiraUseCase(jiraRepo, issueUC, statusChangesUC)
// Handler setup
jiraHandler := jiraFunction.NewJiraHandler(jiraUC)
// Full import
err := jiraHandler.ProcessAndSaveAll("")
if err != nil {
    log.Printf("Error processing Jira data: %v", err)
}
```

## Best Practices
- Always use batch DB operations for large imports.
- Centralize all parsing logic for maintainability.
- Tune rate limits and batch sizes for your infrastructure and Jira API quotas.
- Use the handler layer for orchestration and error reporting.

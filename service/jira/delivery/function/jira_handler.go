package function

import (
	"bitbucket.org/nuitdevelopers/jira-api/service/jira/usecase"
	"context"
	"fmt"
	"log"
	"sync"
)

type <PERSON>ra<PERSON><PERSON><PERSON> struct {
	uc         usecase.JiraUseCase
	IsUpdating bool
	Mu         sync.Mutex
}

func NewJiraHandler(uc usecase.JiraUseCase) *<PERSON>raHandler {
	return &JiraHandler{
		uc: uc,
	}
}

func (h *JiraHandler) SyncAllJiraData(jql string) error {
	h.Mu.Lock()
	h.IsUpdating = true
	fmt.Println(h.IsUpdating)
	defer func() {
		h.IsUpdating = false
		fmt.Println(h.IsUpdating)
		h.Mu.Unlock()
	}()
	log.Println("Starting to process and save all Jira data...")

	ctx := context.Background()

	err := h.uc.ProcessAndSaveIssuesAndStatusChanges(ctx, jql)
	if err != nil {
		log.Printf("Error processing and saving all Jira data: %v", err)
		h.IsUpdating = false
		fmt.Println(h.IsUpdating)
		return err
	}

	log.Println("Successfully completed processing all Jira data")
	fmt.Println("finish", h.IsUpdating)
	return nil
}

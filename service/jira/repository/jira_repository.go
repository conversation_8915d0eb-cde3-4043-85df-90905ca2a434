package repository

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/jira/internal/parser"
	client2 "bitbucket.org/nuitdevelopers/jira-api/service/jira/repository/client"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/joho/godotenv"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

var api_fields = []string{
	"key",
	"summary",
	"status",
	"assignee",
	"reporter",
	"created",
	"updated",
	"priority",
	"issuetype",
	"resolution",
	"resolutiondate",
	"duedate",
	"labels",
	"timeoriginalestimate",
	"components",
	"parent,key",
	"sprint",
	"client",
	"timeremaining",
	"rank",
	"storypoints",
	"project",
	"changelog",
	"customfield_10010",
	"customfield_10008",
	"epic",
	"customfield_10008",
}

type JiraResponse struct {
	Issues     []interface{} `json:"issues"`
	Total      int           `json:"total"`
	StartAt    int           `json:"startAt"`
	MaxResults int           `json:"maxResults"`
}

type JiraRepository struct {
	client *client2.JiraCLient
}

func NewJiraRepository() *JiraRepository {
	client, err := client2.NewJiraClient()
	if err != nil {
		log.Fatalf("Failed to create Jira client: %v", err)
	}

	return &JiraRepository{
		client: client,
	}
}

func (j *JiraRepository) FetchAllIssues(ctx context.Context, jql string) ([]interface{}, error) {
	err := godotenv.Load()
	if err != nil {
		return nil, fmt.Errorf("error loading .env file: %v", err)
	}

	var allIssues []interface{}
	maxResults := j.client.Config.MaxResults
	startAt := 0

	// First request to get total count
	log.Printf("Fetching first page to get total count...")

	firstResponse, err := j.fetchJiraIssuesPage(ctx, jql, startAt, maxResults)
	if err != nil {
		return nil, fmt.Errorf("error fetching first page: %v", err)
	}

	total := firstResponse.Total
	log.Printf("Total issues to fetch: %d", total)
	log.Printf("Max results per page: %d", maxResults)

	// Add first page issues
	allIssues = append(allIssues, firstResponse.Issues...)
	log.Printf("Processed first page: %d issues", len(firstResponse.Issues))

	if total <= maxResults {
		log.Printf("All issues fetched in single page")
		return allIssues, nil
	}

	totalPages := (total + maxResults - 1) / maxResults
	log.Printf("Need to fetch %d pages total", totalPages)

	for page := 1; page < totalPages; page++ {

		select {
		case <-ctx.Done():
			log.Printf("Context cancelled, stopping at page %d", page)
			return allIssues, ctx.Err()
		default:
		}

		startAt := page * maxResults
		log.Printf("Fetching page %d/%d (startAt: %d)", page+1, totalPages, startAt)

		response, err := j.fetchJiraIssuesPage(ctx, jql, startAt, maxResults)
		if err != nil {
			log.Printf("Error fetching page %d: %v", page+1, err)
			continue
		}

		allIssues = append(allIssues, response.Issues...)
		log.Printf("Page %d/%d complete: %d issues", page+1, totalPages, len(response.Issues))
	}

	log.Printf("Successfully fetched %d issues total", len(allIssues))
	return allIssues, nil
}

func (j *JiraRepository) FetchIssuesAndStatuses(ctx context.Context, jql string) ([]interface{}, []model.StatusChange, error) {
	// For status changes, we need the same issues but will extract status change data
	rawIssues, err := j.FetchAllIssues(ctx, jql)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching issues for status changes: %w", err)
	}

	var allStatusChanges []model.StatusChange

	for _, rawIssue := range rawIssues {
		statusChanges, err := j.extractStatusChanges(rawIssue)
		if err != nil {
			log.Printf("Error extracting status changes from issue %v: %v", rawIssue, err)
			continue
		}
		allStatusChanges = append(allStatusChanges, statusChanges...)
	}
	fmt.Println(len(allStatusChanges))
	log.Printf("Successfully extracted %d status changes from %d issues", len(allStatusChanges), len(rawIssues))
	return rawIssues, allStatusChanges, nil
}

func (j *JiraRepository) fetchJiraIssuesPage(ctx context.Context, jql string, startAt, maxResults int) (*JiraResponse, error) {
	baseUrl := fmt.Sprintf("https://api.atlassian.com/ex/jira/%s/rest/api/3/search", j.client.Config.CloudId)

	req, err := http.NewRequestWithContext(ctx, "GET", baseUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	authString := base64.StdEncoding.EncodeToString([]byte(os.Getenv("JIRA_EMAIL") + ":" + j.client.Config.Token))
	req.Header.Add("Authorization", "Basic "+authString)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")

	params := url.Values{}
	params.Add("maxResults", strconv.Itoa(maxResults))
	params.Add("startAt", strconv.Itoa(startAt))
	params.Add("fields", strings.Join(api_fields, ","))
	params.Add("expand", "changelog")
	if jql != "" {
		params.Add("jql", jql)
	}

	req.URL.RawQuery = params.Encode()

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			log.Printf("Error closing response body: %v", closeErr)
		}
	}()

	// Handle rate limiting
	if resp.StatusCode == 429 {
		retryAfter := resp.Header.Get("Retry-After")
		if retryAfter != "" {
			if seconds, err := strconv.Atoi(retryAfter); err == nil {
				log.Printf("Rate limited, waiting %d seconds", seconds)
				time.Sleep(time.Duration(seconds) * time.Second)
				return j.fetchJiraIssuesPage(ctx, jql, startAt, maxResults)
			}
		}
		// Default wait if no Retry-After header
		time.Sleep(5 * time.Second)
		return j.fetchJiraIssuesPage(ctx, jql, startAt, maxResults)
	}

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	var response JiraResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

func (j *JiraRepository) extractStatusChanges(issue interface{}) ([]model.StatusChange, error) {
	return parser.ProcessStatusChangesFromIssue(issue)
}

func (j *JiraRepository) GetDBBatchSize() int {
	return j.client.Config.DBBatchSize
}

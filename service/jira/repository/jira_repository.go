package repository

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/jira/internal/parser"
	client2 "bitbucket.org/nuitdevelopers/jira-api/service/jira/repository/client"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/joho/godotenv"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

var api_fields = []string{
	"key",
	"summary",
	"status",
	"assignee",
	"reporter",
	"created",
	"updated",
	"priority",
	"issuetype",
	"resolution",
	"resolutiondate",
	"duedate",
	"labels",
	"timeoriginalestimate",
	"components",
	"parent,key",
	"sprint",
	"client",
	"timeremaining",
	"rank",
	"storypoints",
	"project",
	"changelog",
	"customfield_10010",
	"customfield_10008",
	"epic",
	"customfield_10008",
}

type JiraResponse struct {
	Issues        []interface{} `json:"issues"`
	Total         int           `json:"total"`
	StartAt       int           `json:"startAt"`
	MaxResults    int           `json:"maxResults"`
	NextPageToken string        `json:"nextPageToken,omitempty"`
	IsLast        bool          `json:"isLast"`
}

type JiraRepository struct {
	client *client2.JiraCLient
}

func NewJiraRepository() *JiraRepository {
	client, err := client2.NewJiraClient()
	if err != nil {
		log.Fatalf("Failed to create Jira client: %v", err)
	}

	return &JiraRepository{
		client: client,
	}
}

func (j *JiraRepository) FetchAllIssues(ctx context.Context, jql string) ([]interface{}, error) {
	err := godotenv.Load()
	if err != nil {
		return nil, fmt.Errorf("error loading .env file: %v", err)
	}

	var allIssues []interface{}
	maxResults := j.client.Config.MaxResults
	nextPageToken := ""
	pageCount := 0

	log.Printf("Starting to fetch issues with enhanced search API...")
	log.Printf("Max results per page: %d", maxResults)

	for {
		pageCount++
		select {
		case <-ctx.Done():
			log.Printf("Context cancelled, stopping at page %d", pageCount)
			return allIssues, ctx.Err()
		default:
		}

		log.Printf("Fetching page %d (nextPageToken: %s)", pageCount, nextPageToken)

		response, err := j.fetchJiraIssuesPageWithToken(ctx, jql, nextPageToken, maxResults)
		if err != nil {
			return nil, fmt.Errorf("error fetching page %d: %v", pageCount, err)
		}

		// Add issues from this page
		allIssues = append(allIssues, response.Issues...)
		log.Printf("Page %d complete: %d issues (total so far: %d)", pageCount, len(response.Issues), len(allIssues))

		// Check if this is the last page
		if response.IsLast || response.NextPageToken == "" {
			log.Printf("Reached last page")
			break
		}

		nextPageToken = response.NextPageToken
	}

	log.Printf("Successfully fetched %d issues total across %d pages", len(allIssues), pageCount)
	return allIssues, nil
}

func (j *JiraRepository) FetchIssuesAndStatuses(ctx context.Context, jql string) ([]interface{}, []model.StatusChange, error) {
	// For status changes, we need the same issues but will extract status change data
	rawIssues, err := j.FetchAllIssues(ctx, jql)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching issues for status changes: %w", err)
	}

	var allStatusChanges []model.StatusChange

	for _, rawIssue := range rawIssues {
		statusChanges, err := j.extractStatusChanges(rawIssue)
		if err != nil {
			log.Printf("Error extracting status changes from issue %v: %v", rawIssue, err)
			continue
		}
		allStatusChanges = append(allStatusChanges, statusChanges...)
	}
	fmt.Println(len(allStatusChanges))
	log.Printf("Successfully extracted %d status changes from %d issues", len(allStatusChanges), len(rawIssues))
	return rawIssues, allStatusChanges, nil
}

func (j *JiraRepository) fetchJiraIssuesPageWithToken(ctx context.Context, jql string, nextPageToken string, maxResults int) (*JiraResponse, error) {
	baseUrl := fmt.Sprintf("https://api.atlassian.com/ex/jira/%s/rest/api/3/search/jql", j.client.Config.CloudId)

	req, err := http.NewRequestWithContext(ctx, "GET", baseUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	authString := base64.StdEncoding.EncodeToString([]byte(os.Getenv("JIRA_EMAIL") + ":" + j.client.Config.Token))
	req.Header.Add("Authorization", "Basic "+authString)
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")

	params := url.Values{}
	params.Add("maxResults", strconv.Itoa(maxResults))
	if nextPageToken != "" {
		params.Add("nextPageToken", nextPageToken)
	}
	params.Add("fields", strings.Join(api_fields, ","))
	params.Add("expand", "changelog")
	if jql != "" {
		params.Add("jql", jql)
	}

	req.URL.RawQuery = params.Encode()

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %v", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			log.Printf("Error closing response body: %v", closeErr)
		}
	}()

	// Handle rate limiting
	if resp.StatusCode == 429 {
		retryAfter := resp.Header.Get("Retry-After")
		if retryAfter != "" {
			if seconds, err := strconv.Atoi(retryAfter); err == nil {
				log.Printf("Rate limited, waiting %d seconds", seconds)
				time.Sleep(time.Duration(seconds) * time.Second)
				return j.fetchJiraIssuesPageWithToken(ctx, jql, nextPageToken, maxResults)
			}
		}
		// Default wait if no Retry-After header
		time.Sleep(5 * time.Second)
		return j.fetchJiraIssuesPageWithToken(ctx, jql, nextPageToken, maxResults)
	}

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}

	var response JiraResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return &response, nil
}

func (j *JiraRepository) extractStatusChanges(issue interface{}) ([]model.StatusChange, error) {
	return parser.ProcessStatusChangesFromIssue(issue)
}

func (j *JiraRepository) GetDBBatchSize() int {
	return j.client.Config.DBBatchSize
}

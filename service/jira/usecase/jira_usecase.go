package usecase

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/issues"

	"bitbucket.org/nuitdevelopers/jira-api/service/jira/repository"
	"bitbucket.org/nuitdevelopers/jira-api/service/statuschanges"
	"context"
	"fmt"
	"log"

	"bitbucket.org/nuitdevelopers/jira-api/service/jira/internal/processor"
)

type JiraUseCase struct {
	jiraRepo        repository.JiraRepository
	issuesUC        issues.UseCase
	statusChangesUC statuschanges.UseCase
	processor       *processor.IssueProcessor
}

func NewJiraUseCase(jiraRepo repository.JiraRepository, issuesUC issues.UseCase, statusChangesUC statuschanges.UseCase) *JiraUseCase {
	return &JiraUseCase{
		jiraRepo:        jiraRepo,
		issuesUC:        issuesUC,
		statusChangesUC: statusChangesUC,
		processor:       &processor.IssueProcessor{},
	}
}

func (uc *JiraUseCase) ProcessAndSaveIssuesAndStatusChanges(ctx context.Context, jql string) error {
	allIssues, allStatusChanges, err := uc.jiraRepo.FetchIssuesAndStatuses(ctx, jql)
	if err != nil {
		return fmt.Errorf("error fetching status changes from repository: %w", err)
	}

	var processedIssues []model.Issue
	for _, rawIssue := range allIssues {
		issue, err := uc.processor.ProcessIssue(rawIssue)
		if err != nil {
			log.Printf("Error processing issue %v: %v", rawIssue, err)
			continue
		}
		processedIssues = append(processedIssues, issue)
	}

	if len(processedIssues) > 0 {
		// Hier wird der Batch in Arrays unterteilt, die dann für das Update verwendet werden

		// Hier sammeln wir alle Batches in einem Slice von Arrays
		var allBatches []model.Issues

		for i := 0; i < len(processedIssues); i += uc.jiraRepo.GetDBBatchSize() {
			end := i + uc.jiraRepo.GetDBBatchSize()
			if end > len(processedIssues) {
				end = len(processedIssues)
			}
			batch := processedIssues[i:end]
			allBatches = append(allBatches, batch) // Füge das aktuelle Batch hinzu
		}

		// Update mit dem Array von Batches
		err := uc.issuesUC.Update(allBatches)
		if err != nil {
			return fmt.Errorf("error updating issues batches: %w", err)
		}

		log.Printf("Successfully updated %d batches of issues", len(allBatches))

		log.Printf("Successfully processed and saved %d issues.", len(processedIssues))

	}

	if len(allStatusChanges) > 0 {
		fmt.Println(len(allStatusChanges))

		var allBatches []model.StatusChanges

		for i := 0; i < len(allStatusChanges); i += uc.jiraRepo.GetDBBatchSize() {
			end := i + uc.jiraRepo.GetDBBatchSize()
			if end > len(allStatusChanges) {
				end = len(allStatusChanges)
			}
			batch := allStatusChanges[i:end]
			allBatches = append(allBatches, batch) // Füge das aktuelle Batch hinzu
		}

		// Update mit dem Array von Batches
		err := uc.statusChangesUC.Update(allBatches)
		if err != nil {
		}

		log.Printf("Successfully updated %d batches of issues", len(allBatches))
	}
	log.Printf("Successfully processed and saved %d issues.", len(processedIssues))

	return nil
}

package mysql

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"gorm.io/gorm"
	"log"
)

var (
	Db *gorm.DB
)

type IssueRepository struct {
}

func NewIssueRepository() *IssueRepository {
	return &IssueRepository{}
}

func (h IssueRepository) Create(command model.Issue) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		sm := IssueFromModel(command)
		err := tx.Create(&sm).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h IssueRepository) CreateMany(commands model.Issues) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		var sms []Issue
		for _, c := range commands {
			sms = append(sms, IssueFromModel(c))
		}
		err := tx.Create(&sms).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h IssueRepository) Update(commands []model.Issues) error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := h.Delete()
		if err != nil {
			return err
		}
		for _, command := range commands {
			err := h.CreateMany(command)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (h IssueRepository) Delete() error {
	err := Db.Transaction(func(tx *gorm.DB) error {
		err := tx.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&Issue{}).Error
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

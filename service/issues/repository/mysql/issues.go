package mysql

import (
	"bitbucket.org/nuitdevelopers/jira-api/config"
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"log"
	"time"
)

type Issue struct {
	Key                  string `gorm:"column:key;size:255;primaryKey"`
	Summary              string `gorm:"column:summary;type:text"`
	AssigneeName         string `gorm:"column:assigneeName;size:255"`
	SecondAssigneeName   string `gorm:"column:secondAssigneeName;size:255"`
	ReporterName         string `gorm:"column:reporterName;size:255"`
	StatusName           string `gorm:"column:statusName;size:255"`
	StatusId             int    `gorm:"column:statusId"`
	IssueTypeName        string `gorm:"column:issueTypeName;size:255"`
	PriorityId           int    `gorm:"column:priorityId"`
	PriorityName         string `gorm:"column:priorityName;size:255"`
	Created              uint64 `gorm:"column:created"`
	Updated              uint64 `gorm:"column:updated"`
	Labels               string `gorm:"column:labels;size:255"`
	Resolution           string `gorm:"column:resolution;size:255"`
	ResolutionDate       uint64 `gorm:"column:resolutionDate"`
	TimeOriginalEstimate int    `gorm:"column:timeOriginalEstimate"`
	TimeRemaining        int    `gorm:"column:timeRemaining"`
	Components           string `gorm:"column:components;size:255"`
	ParentKey            string `gorm:"column:parentKey;size:255"`
	ParentName           string `gorm:"column:parentName;size:255"`
	SprintName           string `gorm:"column:sprintName;size:255"`
	Client               string `gorm:"column:client;size:255"`
	Rank                 string `gorm:"column:rank;size:255"`
	StoryPoints          int    `gorm:"column:storyPoints"`
	ProjectKey           string `gorm:"column:projectKey;size:255"`
	ProjectName          string `gorm:"column:projectName;size:255"`
	DueDate              uint64 `gorm:"column:dueDate"`
}

func (Issue) TableName() string {
	config, err := config.LoadConfig()
	if err != nil {
		log.Printf("Error loading config: %v", err)
	}
	return config.IssueTable
}

/*
func (o Issue) Model() model.Issue {
	return model.Issue{
		Key:                  o.Key,
		Summary:              o.Summary,
		AssigneeName:         o.AssigneeName,
		SecondAssigneeName:   o.SecondAssigneeName,
		ReporterName:         o.ReporterName,
		StatusName:           o.StatusName,
		StatusId:             o.StatusId,
		IssueTypeName:        o.IssueTypeName,
		PriorityId:           o.PriorityId,
		PriorityName:         o.PriorityName,
		Created:              time.Unix(0, int64(o.Created)),
		Updated:              time.Unix(0, int64(o.Updated)),
		Labels:               o.Labels,
		Resolution:           o.Resolution,
		ResolutionDate:       time.Unix(0, int64(o.ResolutionDate)),
		TimeOriginalEstimate: o.TimeOriginalEstimate,
		TimeRemaining:        o.TimeRemaining,
		Components:           o.Components,
		ParentKey:            o.ParentKey,
		ParentName:           o.ParentName,
		SprintName:           o.SprintName,
		Client:               o.Client,
		Rank:                 o.Rank,
		StoryPoints:          o.StoryPoints,
		ProjectKey:           o.ProjectKey,
		ProjectName:          o.ProjectName,
		DueDate:              time.Unix(0, int64(o.DueDate)),
	}
}}*/

func IssueFromModel(mo model.Issue) Issue {
	// Helper function to safely dereference string pointers
	safeString := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	// Helper function to safely dereference int pointers
	safeInt := func(i *int) int {
		if i == nil {
			return 0
		}
		return *i
	}

	// Helper function to safely dereference time pointers
	safeTime := func(t *time.Time) uint64 {
		if t == nil || t.IsZero() {
			return 0
		}
		return uint64(t.UnixNano())
	}

	o := Issue{
		Key:                  mo.Key,
		Summary:              mo.Summary,
		AssigneeName:         safeString(mo.AssigneeName),
		SecondAssigneeName:   safeString(mo.SecondAssigneeName),
		ReporterName:         safeString(mo.ReporterName),
		StatusName:           mo.StatusName,
		StatusId:             mo.StatusId,
		IssueTypeName:        mo.IssueTypeName,
		PriorityId:           mo.PriorityId,
		PriorityName:         mo.PriorityName,
		Created:              uint64(mo.Created.UnixNano()),
		Updated:              uint64(mo.Updated.UnixNano()),
		Labels:               safeString(mo.Labels),
		Resolution:           safeString(mo.Resolution),
		ResolutionDate:       safeTime(mo.ResolutionDate),
		TimeOriginalEstimate: safeInt(mo.TimeOriginalEstimate),
		TimeRemaining:        safeInt(mo.TimeRemaining),
		Components:           safeString(mo.Components),
		ParentKey:            safeString(mo.ParentKey),
		ParentName:           safeString(mo.ParentName),
		SprintName:           safeString(mo.SprintName),
		Client:               safeString(mo.Client),
		Rank:                 safeString(mo.Rank),
		StoryPoints:          safeInt(mo.StoryPoints),
		ProjectKey:           mo.ProjectKey,
		ProjectName:          mo.ProjectName,
		DueDate:              safeTime(mo.DueDate),
	}
	return o
}

type Issues []Issue

/*
func (os Issues) ModelIssue() model.Issues {
	var msc model.Issues
	for _, o := range os {
		msc = append(msc, o.Model())
	}
	return msc
}*/

package usecase

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/issues"
)

type IssueUseCase struct {
	repo issues.Repository
}

func NewIssueUseCase(repo issues.Repository) *IssueUseCase {
	return &IssueUseCase{repo: repo}
}

func (u *IssueUseCase) Create(input model.Issue) error {
	err := u.repo.Create(input)
	return err
}

func (u *IssueUseCase) CreateMany(input model.Issues) error {
	err := u.repo.CreateMany(input)
	return err
}

func (u *IssueUseCase) Update(input []model.Issues) error {
	err := u.repo.Update(input)
	return err
}

func (u *IssueUseCase) Delete() error {
	err := u.repo.Delete()
	return err
}

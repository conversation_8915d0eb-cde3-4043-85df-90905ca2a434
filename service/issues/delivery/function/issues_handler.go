package function

import (
	"bitbucket.org/nuitdevelopers/jira-api/model"
	"bitbucket.org/nuitdevelopers/jira-api/service/issues"
	"log"
)

type IssueHandler struct {
	uc issues.UseCase
}

func NewIssueHandler(uc issues.UseCase) *IssueHandler {
	return &IssueHandler{
		uc: uc,
	}
}
func (h *IssueHandler) Create(input model.Issue) error {
	log.Printf("Saving issue: %s (%s)", input.Key, input.Summary)
	err := h.uc.Create(input)
	if err != nil {
		log.Printf("Error saving issue %s: %v", input.Key, err)
	}
	return err
}

func (h *IssueHandler) CreateMany(input model.Issues) error {
	err := h.uc.Create<PERSON>any(input)
	return err
}

func (h *IssueHandler) Update(input []model.Issues) error {
	err := h.uc.Update(input)
	return err
}

func (h *IssueHandler) Delete() error {
	err := h.uc.Delete()
	return err
}

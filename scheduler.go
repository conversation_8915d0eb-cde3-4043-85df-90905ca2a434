package main

import (
	jiraFunction "bitbucket.org/nuitdevelopers/jira-api/service/jira/delivery/function"
	"context"
	"log"
	"time"
)

var doneChan chan struct{}

func scheduler(autoUpdateTime time.Duration, jiraHandler *jiraFunction.JiraHandler) {
	mu.Lock()
	if cancel != nil {
		cancel()
	}
	if doneChan != nil {
		<-doneChan
	}
	ctx, cancel = context.WithCancel(context.Background())
	doneChan = make(chan struct{})
	mu.Unlock()
	go func() {
		defer close(doneChan)
		for {
			log.Printf("Waiting for auto update in %v ...", autoUpdateTime)
			time.Sleep(autoUpdateTime)
			select {
			case <-ctx.Done():
				log.Println("Auto update stopped")
				return
			default:
				err := jiraHandler.SyncAllJiraData("created >= 2000-01-01")
				if err != nil {
					log.Printf("Error processing Jira data: %v", err)
				}
			}
		}
	}()
}

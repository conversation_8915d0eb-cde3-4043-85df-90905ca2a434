package main

import (
	"bitbucket.org/nuitdevelopers/jira-api/service/auto_update"
	jiraFunction "bitbucket.org/nuitdevelopers/jira-api/service/jira/delivery/function"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/cors"
	"net/http"
	"sync"
	"time"
)

var (
	ctx    context.Context
	cancel context.CancelFunc
	mu     sync.Mutex
)

type JiraRouter struct {
	AutoUpdateConfig *auto_update.AutoUpdateConfig
	JiraHandler      *jiraFunction.JiraHandler
}

func NewJiraRouter(r chi.Router, autoUpdateConfig *auto_update.AutoUpdateConfig, jiraHandler *jiraFunction.JiraHandler) {

	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	h := &JiraRouter{autoUpdateConfig, jiraHandler}

	r.Use(CORSHeadersMiddleware)

	r.Group(func(r chi.Router) {
		r.Get("/status/updating", h.GetIssuesUpdateStatus)
		r.Post("/issues/update", h.UpdateIssuesManually)
		r.Get("/auto-update/interval", h.GetAutoUpdateInterval)
		r.Post("/auto-update/interval", h.SetAutoUpdateInterval)
		r.Post("/auto-update/stop", h.StopAutoUpdate)
		r.Post("/auto-update/start", h.StartAutoUpdate)
		r.Get("/auto-update/status", h.GetAutoUpdateStatus)
	})
}

func CORSHeadersMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		//w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func (router JiraRouter) GetIssuesUpdateStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	response := fmt.Sprintf(`{"updating": %t}`, router.JiraHandler.IsUpdating)
	w.Write([]byte(response))
}

func (router JiraRouter) UpdateIssuesManually(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	if router.JiraHandler.IsUpdating {
		http.Error(w, "Update already in progress", http.StatusConflict)
		return
	}
	if cancel != nil {
		cancel()
	}
	err := router.JiraHandler.SyncAllJiraData("created >= 2000-01-01")
	if err != nil {
		http.Error(w, "Failed to update issues: "+err.Error(), http.StatusInternalServerError)
		return
	}
	autoUpdateTime := time.Duration(router.AutoUpdateConfig.AutoUpdateInterval) * time.Second
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"message": "Issues updated successfully"}`))
	if router.AutoUpdateConfig.AutoUpdateEnabled {
		go func() {
			time.Sleep(20 * time.Second)
			scheduler(autoUpdateTime, router.JiraHandler)
		}()
	} else {
		err := router.JiraHandler.SyncAllJiraData("created >= 2000-01-01")
		if err != nil {
			http.Error(w, "Failed to update issues: "+err.Error(), http.StatusInternalServerError)
			return
		}
	}
}

func (router JiraRouter) GetAutoUpdateInterval(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	response := fmt.Sprintf(`{"auto_update_interval": %d seconds}`, router.AutoUpdateConfig.AutoUpdateInterval)
	w.Write([]byte(response))
}

func (router JiraRouter) SetAutoUpdateInterval(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	var newIntervalResponse IntervalResponse
	err := json.NewDecoder(r.Body).Decode(&newIntervalResponse)
	if err != nil {
		http.Error(w, "Failed to decode request body: "+err.Error(), http.StatusBadRequest)
		return
	}
	fmt.Println(newIntervalResponse.AutoUpdateInterval)
	if newIntervalResponse.AutoUpdateInterval < 1 {
		http.Error(w, "Invalid auto update interval", http.StatusBadRequest)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"message": "interval updated"}`))
	router.AutoUpdateConfig.AutoUpdateInterval = newIntervalResponse.AutoUpdateInterval
	if router.AutoUpdateConfig.AutoUpdateEnabled {
		scheduler(time.Duration(newIntervalResponse.AutoUpdateInterval)*time.Second, router.JiraHandler)
	}
}

func (router JiraRouter) StopAutoUpdate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	mu.Lock()
	if cancel != nil {
		cancel()
		cancel = nil
		router.AutoUpdateConfig.AutoUpdateEnabled = false
	}
	mu.Unlock()

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"message": "Auto update stopped"}`))
}

func (router JiraRouter) StartAutoUpdate(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	autoUpdateTime := time.Duration(router.AutoUpdateConfig.AutoUpdateInterval) * time.Second

	router.AutoUpdateConfig.AutoUpdateEnabled = true
	scheduler(autoUpdateTime, router.JiraHandler)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"message": "Auto update started"}`))
}

func (router JiraRouter) GetAutoUpdateStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	var response string
	if router.AutoUpdateConfig.AutoUpdateEnabled {
		response = fmt.Sprintf(`{"auto_update is enabled"}`)
	} else {
		response = fmt.Sprintf(`{"auto_update is disabled"}`)
	}
	w.Write([]byte(response))
}

package config

import (
	"encoding/json"
	"fmt"
	"os"
)

type JsonConfig struct {
	AutoUpdateEnabled  bool   `json:"auto_update_enabled"`
	AutoUpdateInterval int    `json:"auto_update_interval"`
	IsUpdating         bool   `json:"is_updating"`
	RequestsPerSecond  int    `json:"requests_per_second"`
	MaxResults         int    `json:"max_results"`
	DBBatchSize        int    `json:"db_batch_size"`
	IssueTable         string `json:"issue_table"`
	StatusTable        string `json:"status_table"`
}

func LoadConfig() (*JsonConfig, error) {
	file, err := os.Open("config/config.json")
	if err != nil {
		return nil, fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	var cfg JsonConfig
	err2 := json.NewDecoder(file).Decode(&cfg)
	if err2 != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("error decoding JSON: %v", err2)
	}

	return &cfg, nil
}

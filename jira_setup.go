package main

import (
	issuesMysql "bitbucket.org/nuitdevelopers/jira-api/service/issues/repository/mysql"
	issuesUseCase "bitbucket.org/nuitdevelopers/jira-api/service/issues/usecase"
	jiraFunction "bitbucket.org/nuitdevelopers/jira-api/service/jira/delivery/function"
	jiraRepository "bitbucket.org/nuitdevelopers/jira-api/service/jira/repository"
	jiraUseCase "bitbucket.org/nuitdevelopers/jira-api/service/jira/usecase"
	statusChangesMysql "bitbucket.org/nuitdevelopers/jira-api/service/statuschanges/repository/mysql"
	statusChangesUseCase "bitbucket.org/nuitdevelopers/jira-api/service/statuschanges/usecase"
)

func JiraSetup() *jiraFunction.JiraHandler {
	issueRepo := issuesMysql.NewIssueRepository()
	issueUC := issuesUseCase.NewIssueUseCase(issueRepo)

	statusChangesRepo := statusChangesMysql.NewStatusChangeRepository()
	statusChangesUC := statusChangesUseCase.NewStatusChangeUseCase(statusChangesRepo)

	jiraRepo := jiraRepository.NewJiraRepository()
	jiraUC := jiraUseCase.NewJiraUseCase(*jiraRepo, issueUC, statusChangesUC)
	jiraHandler := jiraFunction.NewJiraHandler(*jiraUC)
	return jiraHandler
}
